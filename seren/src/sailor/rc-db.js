if (!window.chatModule) {
  class Room {
    constructor(roomId, rawRoom, seren) {
      this.id = roomId;
      this.raw = rawRoom;
      this.seren = seren;
      this.members = {};
      this.messages = [];
      this.events = [];
      this.lastMessage = null;
      this.isActive = false;
      this.flaggedStatus = null;
      this._processEvents();
    }

    _processEvents() {
      const stateEvents = this.raw.state?.events || [];
      const timelineEvents = this.raw.timeline?.events || [];

      stateEvents.forEach(event => {
        if (event.type === 'm.room.member') {
          const userId = event.state_key;
          this.members[userId] = {
            userId,
            userName: event.content.displayname || event.content.name || userId,
            accountName: userId.split(':')[0].replace(/^@/, '')
          };
        }
      });

      timelineEvents.forEach(event => {
        if (event.type === 'm.room.message' && !event.unsigned?.redacted_because) {
          const message = this._createMessage(event);
          if (message) {
            this.messages.push(message);
          }
        }
        if ([
          'com.reddit.invite_warning_label',
          'com.reddit.invite_spam_status',
          'com.reddit.spam_status',
          'com.reddit.shadowban_status',
          'com.reddit.user_suspension_status'
        ].includes(event.type)) {
          this.flaggedStatus = {
            type: event.type,
            sender: this.members[event.sender]?.userName || event.sender,
            status: event.content.status,
            time: new Date(event.origin_server_ts).toISOString(),
          };
          this.events.push(this.flaggedStatus);
        }
      });

      this.messages.sort((a, b) => new Date(a.time) - new Date(b.time));
      this.lastMessage = this.messages[this.messages.length - 1] || null;
    }

    _createMessage(event) {
      if (!event.content) return null;
      const { body, "com.reddit.blurred_url": blurredUrl } = event.content;
      const text_message = body || "";
      const content = blurredUrl
        ? { type: 'media', text: { value: '' }, media: { url: blurredUrl, base64_data: "" } }
        : { type: 'text', text: { value: text_message }, media: { url: '', base64_data: '' } };

      return {
        id: event.event_id,
        message_id: event.event_id,
        message_time: new Date(event.origin_server_ts).toISOString(),
        room_id: this.id,
        account_id: this.seren.name,
        time: new Date(event.origin_server_ts).toISOString(),
        message_text: blurredUrl ? "" : text_message,
        user_id: event.sender,
        user_name: this.members[event.sender]?.userName || event.sender,
        content: content
      };
    }

    getOtherMember() {
      const otherMember = Object.values(this.members).find(m => m.userId !== this.seren.account_id);
      return otherMember || null;
    }

    checkActivity(threshold) {
      if (!this.lastMessage) return false;
      const now = Date.now();
      const lastMsgTime = new Date(this.lastMessage.time).getTime();
      this.isActive = (now - lastMsgTime) < threshold;
      return this.isActive;
    }
  }

  class RoomCache {
    constructor(dbQuery) {
      this.db = dbQuery;
      this.rooms = new Map();
      this.seren = { account_id: null, name: 'seren' };
      this.isInitialized = false;
    }

    async initialize() {
      if (this.isInitialized) return;
      await this.db._withTransaction(['accountData', 'sync'], 'readonly', async (tx) => {
        this.seren.account_id = await this.db.execute_cursor(
          tx.objectStore('accountData'),
          (cursor) => {
            const item = cursor.value;
            if (item.type === 'm.push_rules') {
              const pattern = item.content.global.content.find(r => r.rule_id === '.m.rule.contains_user_name').pattern;
              return `@${pattern}:reddit.com`;
            }
            return false;
          }
        );

        await this.db.execute_cursor(tx.objectStore('sync'), (cursor) => {
          const roomsData = cursor.value.roomsData.join;
          if (roomsData) {
            Object.entries(roomsData).forEach(([roomId, roomInfo]) => {
              const room = new Room(roomId, roomInfo, this.seren);
              this.rooms.set(roomId, room);
            });
          }
        });
      });
      this.isInitialized = true;
    }

    getRoom(roomId) {
      return this.rooms.get(roomId);
    }

    getActiveRooms(threshold = 15 * 60 * 1000) {
      const activeRooms = [];
      for (const room of this.rooms.values()) {
        if (room.checkActivity(threshold)) {
          activeRooms.push({
            roomId: room.id,
            sender_name: room.getOtherMember()?.userName || 'Unknown',
            sender: room.lastMessage.user_id,
            time: room.lastMessage.time,
            messageId: room.lastMessage.id
          });
        }
      }
      return activeRooms;
    }

    getFlaggedRooms() {
      const flaggedRooms = {};
      for (const room of this.rooms.values()) {
        if (room.flaggedStatus) {
          flaggedRooms[room.id] = {
            ...room.flaggedStatus,
            members: room.members
          };
        }
      }
      return flaggedRooms;
    }
  }

  class DbQuery {
    constructor() {
      this.db_name = 'matrix-js-sdk:reddit-chat-sync';
      this.db = null;
      this.cache = new RoomCache(this);
    }

    async _ensureCache() {
      if (!this.cache.isInitialized) {
        await this.cache.initialize();
      }
    }

    async get_chat_data(since, room_id = null, numRecent = 0) {
      await this._ensureCache();
      const messages = [];
      const roomsToProcess = room_id ? [this.cache.getRoom(room_id)] : Array.from(this.cache.rooms.values());

      for (const room of roomsToProcess) {
        if (!room) continue;
        const roomMessages = room.messages.filter(m => new Date(m.time).getTime() >= since);
        messages.push(...roomMessages);
      }

      messages.sort((a, b) => new Date(a.time) - new Date(b.time));
      const finalMessages = numRecent > 0 ? messages.slice(-numRecent) : messages;

      return {
        messages: finalMessages,
        seren: this.cache.seren
      };
    }

    async get_flagged_rooms() {
      await this._ensureCache();
      return this.cache.getFlaggedRooms();
    }

    async check_message_is_sent(room_id, msg) {
      await this._ensureCache();
      const room = this.cache.getRoom(room_id);
      if (!room) return false;
      return room.messages.some(m => m.content.text?.value?.trim() === msg.content.text?.value?.trim());
    }

    async get_active_rooms(serenName, threshold = 15 * 60 * 1000) {
      await this._ensureCache();
      return this.cache.getActiveRooms(threshold);
    }

    async get_room_info(room_id, serenName) {
      await this._ensureCache();
      const room = this.cache.getRoom(room_id);
      if (!room) return null;

      return {
        room_id: room.id,
        members: Object.values(room.members),
        events: room.events,
        messages: room.messages,
        seren: this.cache.seren,
        senderName: room.getOtherMember()?.userName || serenName,
        success: true
      };
    }

    async execute_cursor(store, on_success) {
      return new Promise((resolve, reject) => {
        const req = store.openCursor();
        req.onsuccess = () => {
          const cursor = req.result;
          if (cursor) {
            const result = on_success(cursor);
            if (result !== false) resolve(result);
            else cursor.continue();
          } else {
            resolve();
          }
        };
        req.onerror = reject;
      });
    }

    async _withTransaction(storeNames, mode, callback) {
      const db = await this.open_database();
      const tx = db.transaction(storeNames, mode);
      return await callback(tx);
    }

    async open_database() {
      return new Promise((resolve, reject) => {
        if (this.db) {
          resolve(this.db);
          return;
        }
        const openRequest = indexedDB.open(this.db_name, 1);
        openRequest.onerror = (event) => reject('Error opening DB');
        openRequest.onsuccess = (event) => {
          this.db = event.target.result;
          resolve(this.db);
        };
      });
    }
  }

  const chatModule = {
    DbQuery
  };
  window.chatModule = chatModule;
} else {
  // console.log('chatModule already loaded.');
}
